#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Overwatch 2 Real Aimbot - Professional Grade GUI
واجهة احترافية مع تحكم كامل في الإعدادات
"""

import cv2
import numpy as np
import pyautogui
import win32api
import win32con
import win32gui
import time
import threading
import keyboard
import mss
from ctypes import windll, Structure, c_long, byref
import math
import random
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class Point(Structure):
    _fields_ = [("x", c_long), ("y", c_long)]

class OverwatchAimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_gui()
        self.setup_aimbot()
        self.load_settings()

    def setup_gui(self):
        """إعداد الواجهة الاحترافية"""
        self.root.title("Overwatch 2 Professional Aimbot")
        self.root.geometry("500x700")
        self.root.configure(bg='#1a1a1a')
        self.root.resizable(False, False)

        # تطبيق الثيم الأسود
        style = ttk.Style()
        style.theme_use('clam')

        # تخصيص الألوان
        style.configure('TLabel', background='#1a1a1a', foreground='#ffffff', font=('Arial', 10))
        style.configure('TButton', background='#333333', foreground='#ffffff', font=('Arial', 10, 'bold'))
        style.configure('TScale', background='#1a1a1a', troughcolor='#333333', borderwidth=0)
        style.configure('TCheckbutton', background='#1a1a1a', foreground='#ffffff', font=('Arial', 10))
        style.configure('TFrame', background='#1a1a1a')

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=10)

        title_label = tk.Label(title_frame, text="Overwatch 2 Professional Aimbot",
                              font=('Arial', 16, 'bold'), fg='#00ff00', bg='#1a1a1a')
        title_label.pack()

        subtitle_label = tk.Label(title_frame, text="واجهة احترافية للتحكم الكامل",
                                 font=('Arial', 10), fg='#cccccc', bg='#1a1a1a')
        subtitle_label.pack()

        # إطار الحالة
        status_frame = tk.Frame(self.root, bg='#2a2a2a', relief='raised', bd=2)
        status_frame.pack(fill='x', padx=10, pady=5)

        self.status_label = tk.Label(status_frame, text="الحالة: متوقف",
                                    font=('Arial', 12, 'bold'), fg='#ff4444', bg='#2a2a2a')
        self.status_label.pack(pady=5)

        # أزرار التحكم الرئيسية
        control_frame = tk.Frame(self.root, bg='#1a1a1a')
        control_frame.pack(pady=10)

        self.start_button = tk.Button(control_frame, text="بدء التشغيل",
                                     command=self.start_aimbot, bg='#00aa00', fg='white',
                                     font=('Arial', 12, 'bold'), width=12, height=2)
        self.start_button.pack(side='left', padx=5)

        self.stop_button = tk.Button(control_frame, text="إيقاف",
                                    command=self.stop_aimbot, bg='#aa0000', fg='white',
                                    font=('Arial', 12, 'bold'), width=12, height=2)
        self.stop_button.pack(side='left', padx=5)

        # إعدادات Aim Assist
        self.create_aim_settings()

        # إعدادات التتبع
        self.create_tracking_settings()

        # إعدادات الارتداد
        self.create_recoil_settings()

        # إعدادات الألوان
        self.create_color_settings()

        # أزرار الإعدادات
        self.create_setting_buttons()

    def create_aim_settings(self):
        """إعدادات Aim Assist"""
        aim_frame = tk.LabelFrame(self.root, text="إعدادات Aim Assist",
                                 bg='#1a1a1a', fg='#ffffff', font=('Arial', 11, 'bold'))
        aim_frame.pack(fill='x', padx=10, pady=5)

        # قوة Aim Assist
        tk.Label(aim_frame, text="قوة Aim Assist:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=5)
        self.aim_strength_var = tk.DoubleVar(value=95)
        self.aim_strength_scale = tk.Scale(aim_frame, from_=0, to=100, orient='horizontal',
                                          variable=self.aim_strength_var, bg='#1a1a1a', fg='#ffffff',
                                          highlightbackground='#1a1a1a', troughcolor='#333333')
        self.aim_strength_scale.pack(fill='x', padx=5, pady=2)

        # سرعة التتبع
        tk.Label(aim_frame, text="سرعة التتبع:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=5)
        self.tracking_speed_var = tk.DoubleVar(value=80)
        self.tracking_speed_scale = tk.Scale(aim_frame, from_=0, to=100, orient='horizontal',
                                           variable=self.tracking_speed_var, bg='#1a1a1a', fg='#ffffff',
                                           highlightbackground='#1a1a1a', troughcolor='#333333')
        self.tracking_speed_scale.pack(fill='x', padx=5, pady=2)

    def create_tracking_settings(self):
        """إعدادات التتبع"""
        track_frame = tk.LabelFrame(self.root, text="إعدادات التتبع المتقدم",
                                   bg='#1a1a1a', fg='#ffffff', font=('Arial', 11, 'bold'))
        track_frame.pack(fill='x', padx=10, pady=5)

        # تتبع أثناء الإطلاق
        self.track_while_shooting_var = tk.BooleanVar(value=True)
        tk.Checkbutton(track_frame, text="تتبع العدو أثناء الإطلاق",
                      variable=self.track_while_shooting_var, bg='#1a1a1a', fg='#ffffff',
                      selectcolor='#333333', activebackground='#1a1a1a').pack(anchor='w', padx=5, pady=2)

        # نطاق البحث
        tk.Label(track_frame, text="نطاق البحث (بكسل):", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=5)
        self.search_radius_var = tk.IntVar(value=200)
        self.search_radius_scale = tk.Scale(track_frame, from_=50, to=500, orient='horizontal',
                                          variable=self.search_radius_var, bg='#1a1a1a', fg='#ffffff',
                                          highlightbackground='#1a1a1a', troughcolor='#333333')
        self.search_radius_scale.pack(fill='x', padx=5, pady=2)

        # تنعيم الحركة
        tk.Label(track_frame, text="تنعيم الحركة:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=5)
        self.smoothing_var = tk.DoubleVar(value=10)
        self.smoothing_scale = tk.Scale(track_frame, from_=0, to=50, orient='horizontal',
                                       variable=self.smoothing_var, bg='#1a1a1a', fg='#ffffff',
                                       highlightbackground='#1a1a1a', troughcolor='#333333')
        self.smoothing_scale.pack(fill='x', padx=5, pady=2)

    def create_recoil_settings(self):
        """إعدادات التحكم في الارتداد"""
        recoil_frame = tk.LabelFrame(self.root, text="إعدادات التحكم في الارتداد",
                                    bg='#1a1a1a', fg='#ffffff', font=('Arial', 11, 'bold'))
        recoil_frame.pack(fill='x', padx=10, pady=5)

        # تفعيل التحكم في الارتداد
        self.recoil_control_var = tk.BooleanVar(value=True)
        tk.Checkbutton(recoil_frame, text="تفعيل التحكم في الارتداد",
                      variable=self.recoil_control_var, bg='#1a1a1a', fg='#ffffff',
                      selectcolor='#333333', activebackground='#1a1a1a').pack(anchor='w', padx=5, pady=2)

        # قوة التحكم في الارتداد
        tk.Label(recoil_frame, text="قوة التحكم في الارتداد:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=5)
        self.recoil_strength_var = tk.DoubleVar(value=70)
        self.recoil_strength_scale = tk.Scale(recoil_frame, from_=0, to=100, orient='horizontal',
                                            variable=self.recoil_strength_var, bg='#1a1a1a', fg='#ffffff',
                                            highlightbackground='#1a1a1a', troughcolor='#333333')
        self.recoil_strength_scale.pack(fill='x', padx=5, pady=2)

    def create_color_settings(self):
        """إعدادات كشف الألوان"""
        color_frame = tk.LabelFrame(self.root, text="إعدادات كشف ألوان الأعداء",
                                   bg='#1a1a1a', fg='#ffffff', font=('Arial', 11, 'bold'))
        color_frame.pack(fill='x', padx=10, pady=5)

        # الألوان المختلفة
        colors_grid = tk.Frame(color_frame, bg='#1a1a1a')
        colors_grid.pack(fill='x', padx=5, pady=5)

        self.color_vars = {}
        colors = [('أحمر', 'red', True), ('بنفسجي', 'purple', True),
                 ('وردي', 'pink', True), ('برتقالي', 'orange', False)]

        for i, (name, key, default) in enumerate(colors):
            self.color_vars[key] = tk.BooleanVar(value=default)
            cb = tk.Checkbutton(colors_grid, text=name, variable=self.color_vars[key],
                               bg='#1a1a1a', fg='#ffffff', selectcolor='#333333',
                               activebackground='#1a1a1a')
            cb.grid(row=i//2, column=i%2, sticky='w', padx=5, pady=2)

    def create_setting_buttons(self):
        """أزرار الإعدادات"""
        settings_frame = tk.Frame(self.root, bg='#1a1a1a')
        settings_frame.pack(pady=10)

        save_btn = tk.Button(settings_frame, text="حفظ الإعدادات",
                            command=self.save_settings, bg='#0066cc', fg='white',
                            font=('Arial', 10, 'bold'), width=15)
        save_btn.pack(side='left', padx=5)

        load_btn = tk.Button(settings_frame, text="تحميل الإعدادات",
                            command=self.load_settings, bg='#cc6600', fg='white',
                            font=('Arial', 10, 'bold'), width=15)
        load_btn.pack(side='left', padx=5)

    def setup_aimbot(self):
        """إعداد محرك الـ Aimbot"""
        self.running = False
        self.aim_assist_active = False

        # ألوان الأعداء في Overwatch 2
        self.enemy_colors = {
            'red': ([0, 0, 150], [80, 80, 255]),
            'purple': ([100, 0, 100], [255, 100, 255]),
            'pink': ([150, 100, 150], [255, 200, 255]),
            'orange': ([0, 100, 200], [100, 200, 255])
        }

        self.setup_screen()

    def setup_screen(self):
        """إعداد معلومات الشاشة"""
        self.screen_width = win32api.GetSystemMetrics(0)
        self.screen_height = win32api.GetSystemMetrics(1)
        self.screen_center = (self.screen_width // 2, self.screen_height // 2)

        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0

    def get_cursor_pos(self):
        """الحصول على موقع المؤشر"""
        pt = Point()
        windll.user32.GetCursorPos(byref(pt))
        return pt.x, pt.y

    def move_mouse_smooth(self, target_x, target_y):
        """تحريك الماوس بسلاسة"""
        current_x, current_y = self.get_cursor_pos()

        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)

        if distance < 5:
            return

        # تطبيق الإعدادات من الواجهة
        aim_strength = self.aim_strength_var.get() / 100.0
        tracking_speed = self.tracking_speed_var.get() / 100.0
        smoothing = self.smoothing_var.get() / 100.0

        move_x = dx * aim_strength * tracking_speed
        move_y = dy * aim_strength * tracking_speed

        # إضافة تنعيم
        move_x += random.uniform(-1, 1) * smoothing
        move_y += random.uniform(-1, 1) * smoothing

        new_x = int(current_x + move_x)
        new_y = int(current_y + move_y)

        win32api.SetCursorPos((new_x, new_y))

    def capture_screen_region(self):
        """التقاط منطقة من الشاشة"""
        current_x, current_y = self.get_cursor_pos()
        search_radius = self.search_radius_var.get()

        left = max(0, current_x - search_radius)
        top = max(0, current_y - search_radius)
        width = min(search_radius * 2, self.screen_width - left)
        height = min(search_radius * 2, self.screen_height - top)

        with mss.mss() as sct:
            monitor = {"top": top, "left": left, "width": width, "height": height}
            screenshot = sct.grab(monitor)
            img = np.array(screenshot)
            return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR), left, top

    def detect_enemies(self, img, offset_x, offset_y):
        """كشف الأعداء بناءً على الألوان المحددة"""
        targets = []

        for color_key, (lower, upper) in self.enemy_colors.items():
            if not self.color_vars[color_key].get():
                continue

            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            lower_np = np.array(lower)
            upper_np = np.array(upper)
            mask = cv2.inRange(hsv, lower_np, upper_np)

            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 50:
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"]) + offset_x
                        cy = int(M["m01"] / M["m00"]) + offset_y
                        targets.append((cx, cy, area, color_key))

        return targets

    def get_best_target(self, targets):
        """اختيار أفضل هدف"""
        if not targets:
            return None

        current_x, current_y = self.get_cursor_pos()
        scored_targets = []

        for x, y, area, color in targets:
            distance = math.sqrt((x - current_x)**2 + (y - current_y)**2)
            score = area / (distance + 1)
            scored_targets.append((score, x, y, color))

        scored_targets.sort(reverse=True)
        return scored_targets[0][1:3]

    def apply_recoil_control(self):
        """تطبيق التحكم في الارتداد"""
        if not self.recoil_control_var.get():
            return

        current_x, current_y = self.get_cursor_pos()
        recoil_strength = self.recoil_strength_var.get() / 100.0
        recoil_y = int(random.randint(2, 5) * recoil_strength)
        win32api.SetCursorPos((current_x, current_y + recoil_y))

    def aim_assist_loop(self):
        """حلقة التتبع الرئيسية"""
        while self.running:
            try:
                if self.aim_assist_active:
                    img, offset_x, offset_y = self.capture_screen_region()
                    targets = self.detect_enemies(img, offset_x, offset_y)
                    best_target = self.get_best_target(targets)

                    if best_target:
                        target_x, target_y = best_target
                        self.move_mouse_smooth(target_x, target_y)

                    # تطبيق التحكم في الارتداد أثناء الإطلاق
                    if self.track_while_shooting_var.get() and keyboard.is_pressed('mouse left'):
                        self.apply_recoil_control()

                time.sleep(0.01)

            except Exception as e:
                print(f"خطأ في حلقة التتبع: {e}")
                time.sleep(0.1)

    def start_aimbot(self):
        """بدء تشغيل الـ Aimbot"""
        if not self.running:
            self.running = True
            self.aim_assist_active = True

            # بدء حلقة التتبع
            self.aim_thread = threading.Thread(target=self.aim_assist_loop, daemon=True)
            self.aim_thread.start()

            # تحديث الواجهة
            self.status_label.config(text="الحالة: يعمل", fg='#00ff00')
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')

            messagebox.showinfo("تم البدء", "تم بدء تشغيل الـ Aimbot بنجاح!")

    def stop_aimbot(self):
        """إيقاف الـ Aimbot"""
        self.running = False
        self.aim_assist_active = False

        # تحديث الواجهة
        self.status_label.config(text="الحالة: متوقف", fg='#ff4444')
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')

        messagebox.showinfo("تم الإيقاف", "تم إيقاف الـ Aimbot")

    def save_settings(self):
        """حفظ الإعدادات"""
        settings = {
            'aim_strength': self.aim_strength_var.get(),
            'tracking_speed': self.tracking_speed_var.get(),
            'search_radius': self.search_radius_var.get(),
            'smoothing': self.smoothing_var.get(),
            'recoil_control': self.recoil_control_var.get(),
            'recoil_strength': self.recoil_strength_var.get(),
            'track_while_shooting': self.track_while_shooting_var.get(),
            'colors': {key: var.get() for key, var in self.color_vars.items()}
        }

        try:
            with open('aimbot_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            messagebox.showinfo("تم الحفظ", "تم حفظ الإعدادات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists('aimbot_settings.json'):
                with open('aimbot_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.aim_strength_var.set(settings.get('aim_strength', 95))
                self.tracking_speed_var.set(settings.get('tracking_speed', 80))
                self.search_radius_var.set(settings.get('search_radius', 200))
                self.smoothing_var.set(settings.get('smoothing', 10))
                self.recoil_control_var.set(settings.get('recoil_control', True))
                self.recoil_strength_var.set(settings.get('recoil_strength', 70))
                self.track_while_shooting_var.set(settings.get('track_while_shooting', True))

                colors = settings.get('colors', {})
                for key, var in self.color_vars.items():
                    var.set(colors.get(key, True))

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def run(self):
        """تشغيل الواجهة"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """عند إغلاق البرنامج"""
        if self.running:
            self.stop_aimbot()
        self.root.destroy()
        
    def setup_screen(self):
        """إعداد معلومات الشاشة"""
        self.screen_width = win32api.GetSystemMetrics(0)
        self.screen_height = win32api.GetSystemMetrics(1)
        self.screen_center = (self.screen_width // 2, self.screen_height // 2)
        
        # تعطيل fail-safe في pyautogui
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0
        
    def get_cursor_pos(self):
        """الحصول على موقع المؤشر الحالي"""
        pt = Point()
        windll.user32.GetCursorPos(byref(pt))
        return pt.x, pt.y
        
    def move_mouse_smooth(self, target_x, target_y):
        """تحريك الماوس بسلاسة نحو الهدف"""
        current_x, current_y = self.get_cursor_pos()
        
        # حساب المسافة والاتجاه
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 5:  # إذا كان قريب جداً
            return
            
        # تطبيق قوة التتبع
        move_x = dx * self.aim_strength * self.tracking_speed
        move_y = dy * self.aim_strength * self.tracking_speed
        
        # إضافة تنعيم طبيعي
        move_x += random.uniform(-1, 1) * self.smoothing
        move_y += random.uniform(-1, 1) * self.smoothing
        
        # تحريك الماوس
        new_x = int(current_x + move_x)
        new_y = int(current_y + move_y)
        
        # استخدام win32api للحركة المباشرة
        win32api.SetCursorPos((new_x, new_y))
        
    def capture_screen_region(self):
        """التقاط منطقة من الشاشة حول المؤشر"""
        current_x, current_y = self.get_cursor_pos()
        
        # تحديد منطقة البحث
        left = max(0, current_x - self.search_radius)
        top = max(0, current_y - self.search_radius)
        width = min(self.search_radius * 2, self.screen_width - left)
        height = min(self.search_radius * 2, self.screen_height - top)
        
        with mss.mss() as sct:
            monitor = {
                "top": top,
                "left": left,
                "width": width,
                "height": height
            }
            screenshot = sct.grab(monitor)
            img = np.array(screenshot)
            return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR), left, top
            
    def detect_enemies(self, img, offset_x, offset_y):
        """كشف الأعداء بناءً على الألوان"""
        targets = []
        
        for color_name, (lower, upper) in self.enemy_colors.items():
            # تحويل إلى HSV للكشف الأفضل
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # إنشاء قناع للون
            lower_np = np.array(lower)
            upper_np = np.array(upper)
            mask = cv2.inRange(hsv, lower_np, upper_np)
            
            # تطبيق مرشحات لتحسين الكشف
            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # العثور على الكونتورات
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 50:  # تصفية الأهداف الصغيرة
                    # حساب مركز الهدف
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"]) + offset_x
                        cy = int(M["m01"] / M["m00"]) + offset_y
                        targets.append((cx, cy, area, color_name))
                        
        return targets
        
    def get_best_target(self, targets):
        """اختيار أفضل هدف للتتبع"""
        if not targets:
            return None
            
        current_x, current_y = self.get_cursor_pos()
        
        # ترتيب الأهداف حسب القرب والحجم
        scored_targets = []
        for x, y, area, color in targets:
            distance = math.sqrt((x - current_x)**2 + (y - current_y)**2)
            # تفضيل الأهداف القريبة والكبيرة
            score = area / (distance + 1)
            scored_targets.append((score, x, y, color))
            
        # إرجاع أفضل هدف
        scored_targets.sort(reverse=True)
        return scored_targets[0][1:3]  # إرجاع x, y فقط
        
    def apply_recoil_control(self):
        """تطبيق التحكم في الارتداد"""
        if not self.recoil_control_active:
            return
            
        # تحريك الماوس لأسفل لمقاومة الارتداد
        current_x, current_y = self.get_cursor_pos()
        recoil_y = random.randint(2, 5)  # حركة عشوائية لأسفل
        win32api.SetCursorPos((current_x, current_y + recoil_y))
        
    def aim_assist_loop(self):
        """حلقة التتبع الرئيسية"""
        while self.running:
            try:
                if self.aim_assist_active:
                    # التقاط الشاشة
                    img, offset_x, offset_y = self.capture_screen_region()
                    
                    # كشف الأعداء
                    targets = self.detect_enemies(img, offset_x, offset_y)
                    
                    # اختيار أفضل هدف
                    best_target = self.get_best_target(targets)
                    
                    if best_target:
                        target_x, target_y = best_target
                        # تحريك المؤشر نحو الهدف
                        self.move_mouse_smooth(target_x, target_y)
                        self.last_target = best_target
                        
                    # تطبيق التحكم في الارتداد إذا كان مفعل
                    if keyboard.is_pressed('mouse left'):
                        self.apply_recoil_control()
                        
                time.sleep(0.01)  # تحديث سريع جداً
                
            except Exception as e:
                print(f"خطأ في حلقة التتبع: {e}")
                time.sleep(0.1)
                
    def start(self):
        """بدء تشغيل البرنامج"""
        print("=== Overwatch 2 Real Aimbot ===")
        print("ALT: تفعيل/إيقاف Aim Assist")
        print("CTRL: تفعيل/إيقاف Recoil Control") 
        print("INS: إيقاف البرنامج")
        print("البرنامج يعمل الآن...")
        
        self.running = True
        
        # بدء حلقة التتبع في خيط منفصل
        aim_thread = threading.Thread(target=self.aim_assist_loop, daemon=True)
        aim_thread.start()
        
        # حلقة التحكم الرئيسية
        while self.running:
            try:
                # تبديل Aim Assist بـ ALT
                if keyboard.is_pressed('alt'):
                    if not hasattr(self, 'alt_pressed'):
                        self.alt_pressed = True
                        self.aim_assist_active = not self.aim_assist_active
                        status = "مفعل" if self.aim_assist_active else "معطل"
                        print(f"Aim Assist: {status}")
                        time.sleep(0.3)
                else:
                    self.alt_pressed = False
                    
                # تبديل Recoil Control بـ CTRL
                if keyboard.is_pressed('ctrl'):
                    if not hasattr(self, 'ctrl_pressed'):
                        self.ctrl_pressed = True
                        self.recoil_control_active = not self.recoil_control_active
                        status = "مفعل" if self.recoil_control_active else "معطل"
                        print(f"Recoil Control: {status}")
                        time.sleep(0.3)
                else:
                    self.ctrl_pressed = False
                    
                # إيقاف البرنامج بـ INS
                if keyboard.is_pressed('insert'):
                    print("إيقاف البرنامج...")
                    self.running = False
                    break
                    
                time.sleep(0.05)
                
            except KeyboardInterrupt:
                self.running = False
                break
                
        print("تم إيقاف البرنامج.")

if __name__ == "__main__":
    try:
        app = OverwatchAimbotGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
