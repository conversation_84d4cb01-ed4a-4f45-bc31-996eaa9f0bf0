# Overwatch 2 Professional Aimbot GUI

## واجهة احترافية عصرية للتحكم الكامل في الـ Aimbot

### المميزات الجديدة:
- **واجهة احترافية سوداء** - تصميم عصري وأنيق
- **تحكم كامل في الإعدادات** - جميع الخيارات قابلة للتعديل
- **تتبع حقيقي للأعداء** - يعمل فعلياً داخل اللعبة
- **كشف ألوان متقدم** - أحمر، بنفسجي، وردي، برتقالي
- **تحكم في الارتداد** - قابل للتخصيص
- **حفظ وتحميل الإعدادات** - لا تفقد إعداداتك
- **واجهة عربية** - سهلة الاستخدام

### الإعدادات المتاحة:

#### إعدادات Aim Assist:
- **قوة Aim Assist** (0-100%)
- **سرعة التتبع** (0-100%)

#### إعدادات التتبع المتقدم:
- **تتبع أثناء الإطلاق** ✓
- **نطاق البحث** (50-500 بكسل)
- **تنعيم الحركة** (0-50%)

#### إعدادات التحكم في الارتداد:
- **تفعيل التحكم في الارتداد** ✓
- **قوة التحكم** (0-100%)

#### إعدادات كشف الألوان:
- **أحمر** ✓ **بنفسجي** ✓ **وردي** ✓ **برتقالي**

### طريقة التشغيل:

1. **تشغيل البرنامج:**
   ```
   double-click على run_aimbot.bat
   ```

2. **استخدام الواجهة:**
   - اضبط الإعدادات حسب تفضيلك
   - اضغط "بدء التشغيل" لتفعيل الـ Aimbot
   - اضغط "إيقاف" لإيقاف الـ Aimbot
   - احفظ إعداداتك بـ "حفظ الإعدادات"

### كيفية الاستخدام في اللعبة:

1. شغل Overwatch 2
2. شغل البرنامج بـ run_aimbot.bat
3. اضغط ALT لتفعيل Aim Assist
4. اضغط CTRL لتفعيل Recoil Control
5. ابدأ اللعب - السكريبت سيتبع الأعداء تلقائياً

### الإعدادات المتقدمة:

- **قوة التتبع:** 95% (عالية جداً)
- **سرعة التتبع:** 80% (سريعة)
- **نطاق البحث:** 200 بكسل حول المؤشر
- **ألوان الكشف:** أحمر، بنفسجي، وردي، برتقالي

### ملاحظات مهمة:

- يعمل فقط مع Overwatch 2
- يتطلب تشغيل البرنامج كمدير (Administrator)
- تأكد من أن اللعبة في وضع Windowed أو Borderless
- لا تحرك الماوس بقوة أثناء التتبع

### استكشاف الأخطاء:

إذا لم يعمل السكريبت:
1. تأكد من تشغيله كمدير
2. تأكد من أن Python مثبت
3. تأكد من أن جميع المكتبات مثبتة
4. تأكد من أن اللعبة مفتوحة

---
**تحذير:** استخدم هذا البرنامج على مسؤوليتك الخاصة
